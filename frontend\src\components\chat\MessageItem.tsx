"use client";

import { useState, useEffect, useRef } from "react";
import { ChatMessage } from "@/types/chat";

import { Flag, MoreHorizontal, Check, CheckChe<PERSON>, Clock } from "lucide-react";

interface MessageItemProps {
  message: ChatMessage;
  isOwnMessage: boolean;
  onReply?: (message: ChatMessage) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, emoji: string) => void;
  onReport?: (messageId: string) => void;
  allMessages?: ChatMessage[]; // Add this to find replied messages
  currentUserId?: string; // For authenticated users
  currentAnonymousId?: string; // For guest users
}

export const MessageItem: React.FC<MessageItemProps> = ({
  message,
  isOwnMessage,
  onReply,
  onEdit,
  onDelete,
  onReaction,
  onReport,
  allMessages = [],
  currentUserId,
  currentAnonymousId,
}) => {
  const [showActions, setShowActions] = useState(false);
  const [showReactions, setShowReactions] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content.text || "");
  const isSystemMessage = message.content.type === "system";
  const reactionPickerRef = useRef<HTMLDivElement>(null);

  // Close reaction picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        reactionPickerRef.current &&
        !reactionPickerRef.current.contains(event.target as Node)
      ) {
        setShowReactions(false);
      }
    };

    if (showReactions) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [showReactions]);

  // Find the replied message
  const repliedMessage = message.replyTo
    ? allMessages.find((msg) => msg._id === message.replyTo)
    : null;

  const formatTime = (dateString: string | Date) => {
    if (!dateString) {
      console.warn("No date provided to formatTime");
      return "Just now";
    }

    // Handle various date formats
    let date: Date;

    try {
      if (dateString instanceof Date) {
        date = dateString;
      } else if (typeof dateString === "string") {
        // Handle MongoDB ObjectId timestamp extraction
        if (dateString.length === 24 && /^[0-9a-fA-F]{24}$/.test(dateString)) {
          // Extract timestamp from MongoDB ObjectId (first 8 characters represent timestamp)
          const timestamp = parseInt(dateString.substring(0, 8), 16) * 1000;
          date = new Date(timestamp);
        } else {
          // Try parsing as ISO string first
          date = new Date(dateString);

          // If that fails and it's a number string, try parsing as timestamp
          if (isNaN(date.getTime()) && !isNaN(Number(dateString))) {
            const numericDate = Number(dateString);
            // Handle both seconds and milliseconds timestamps
            date = new Date(
              numericDate > 1e10 ? numericDate : numericDate * 1000
            );
          }
        }
      } else {
        console.warn(
          "Invalid date type received:",
          typeof dateString,
          dateString
        );
        return "Just now";
      }

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn("Invalid date received:", dateString);
        return "Just now";
      }

      const now = new Date();
      const diffInMinutes =
        Math.abs(now.getTime() - date.getTime()) / (1000 * 60);

      // If less than 1 minute ago, show "Just now"
      if (diffInMinutes < 1) {
        return "Just now";
      }

      // If less than 1 hour ago, show minutes
      if (diffInMinutes < 60) {
        return `${Math.floor(diffInMinutes)}m ago`;
      }

      const diffInHours = diffInMinutes / 60;

      // If less than 24 hours ago, show hours or time
      if (diffInHours < 24) {
        if (diffInHours < 6) {
          return `${Math.floor(diffInHours)}h ago`;
        } else {
          return date.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          });
        }
      }

      // If more than 24 hours ago, show date
      return date.toLocaleDateString([], {
        month: "short",
        day: "numeric",
        year: date.getFullYear() !== now.getFullYear() ? "numeric" : undefined,
      });
    } catch (error) {
      console.error("Error formatting date:", error, dateString);
      return "Just now";
    }
  };

  const getMessageContent = () => {
    switch (message.content.type) {
      case "text":
        return message.content.text;
      case "image":
        return (
          <div className="mt-2">
            <img
              src={message.content.fileUrl}
              alt={message.content.fileName || "Shared image"}
              className="max-w-sm rounded-lg shadow-sm"
            />
          </div>
        );
      case "file":
        return (
          <div className="mt-2 p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center space-x-2">
              <svg
                className="w-5 h-5 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {message.content.fileName}
                </p>
                <p className="text-xs text-gray-500">
                  {message.content.fileSize
                    ? `${(message.content.fileSize / 1024).toFixed(1)} KB`
                    : ""}
                </p>
              </div>
            </div>
          </div>
        );
      case "system":
        return (
          <div className="text-center py-2">
            <span className="text-sm text-gray-500 italic">
              {message.content.text}
            </span>
          </div>
        );
      default:
        return message.content.text;
    }
  };

  const commonReactions = ["👍", "❤️", "😊", "😢", "😮", "😡"];

  // Helper function to check if current user has reacted with a specific emoji
  const hasUserReacted = (reaction: any) => {
    return reaction.users.some(
      (user: any) =>
        (currentUserId && user.userId === currentUserId) ||
        (currentAnonymousId && user.anonymousId === currentAnonymousId)
    );
  };

  if (isSystemMessage) {
    return (
      <div className="flex justify-center my-4">
        <div className="bg-gray-100 rounded-full px-4 py-2">
          <span className="text-sm text-gray-600">{message.content.text}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`group relative flex ${
        isOwnMessage ? "justify-end" : "justify-start"
      } mb-4`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div
        className={`max-w-xs lg:max-w-md ${
          isOwnMessage ? "order-2" : "order-1"
        }`}
      >
        {/* WhatsApp-Style Message Header */}
        <div
          className={`flex items-center space-x-2 mb-1 ${
            isOwnMessage ? "justify-end" : "justify-start"
          }`}
        >
          {!isOwnMessage && (
            <span className="text-sm font-medium text-gray-900">
              {message.senderDisplayName}
            </span>
          )}
          <span className="text-xs text-gray-500">
            {formatTime(message.createdAt)}
          </span>
          {message.isEdited && (
            <span className="text-xs text-gray-400">(edited)</span>
          )}
          {/* WhatsApp-Style Status Indicators for Own Messages */}
          {isOwnMessage && (
            <div className="flex items-center space-x-1">
              {message.status === "sending" && (
                <Clock className="w-3 h-3 text-gray-400" />
              )}
              {message.status === "sent" && (
                <Check className="w-3 h-3 text-gray-400" />
              )}
              {message.status === "delivered" && (
                <CheckCheck className="w-3 h-3 text-gray-400" />
              )}
              {message.status === "read" && (
                <CheckCheck className="w-3 h-3 text-blue-500" />
              )}
            </div>
          )}
        </div>

        {/* Reply Context */}
        {message.replyTo && (
          <div className="mb-2 p-2 bg-gray-50 rounded border-l-2 border-gray-300">
            {repliedMessage ? (
              <div>
                <p className="text-xs text-gray-600 mb-1">
                  Replying to {repliedMessage.senderDisplayName}:
                </p>
                <p className="text-sm text-gray-700 truncate">
                  {repliedMessage.content.text || "Media message"}
                </p>
              </div>
            ) : (
              <p className="text-xs text-gray-600">Replying to a message</p>
            )}
          </div>
        )}

        {/* WhatsApp-Style Message Bubble */}
        <div
          className={`relative px-3 py-2 break-words overflow-wrap-anywhere shadow-sm ${
            isOwnMessage
              ? "bg-primary-500 text-white rounded-l-2xl rounded-tr-2xl rounded-br-md"
              : "bg-white text-gray-900 rounded-r-2xl rounded-tl-2xl rounded-bl-md border border-gray-200"
          }`}
        >
          {isEditing ? (
            <div className="space-y-2">
              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none"
                rows={3}
              />
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    if (
                      editContent.trim() &&
                      editContent !== message.content.text
                    ) {
                      onEdit?.(message._id, editContent.trim());
                    }
                    setIsEditing(false);
                  }}
                  className="px-3 py-1 bg-primary-600 text-white rounded text-sm hover:bg-primary-700 transition-colors"
                >
                  Save
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setEditContent(message.content.text || "");
                  }}
                  className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            getMessageContent()
          )}

          {/* Floating Action Buttons */}
          {showActions && !isSystemMessage && (
            <div
              className={`absolute top-0 z-[100] ${
                isOwnMessage ? "-left-20" : "-right-20"
              } flex items-center space-x-1 bg-white border border-gray-200 rounded-lg shadow-lg px-2 py-1`}
              style={{ zIndex: 100 }}
            >
              <button
                onClick={() => setShowReactions(!showReactions)}
                className="text-gray-400 hover:text-yellow-500 p-1 rounded transition-colors"
                title="Add reaction"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </button>

              <button
                onClick={() => onReply?.(message)}
                className="text-gray-400 hover:text-blue-500 p-1 rounded transition-colors"
                title="Reply"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
                  />
                </svg>
              </button>

              {isOwnMessage && (
                <>
                  <button
                    onClick={() => {
                      setIsEditing(true);
                      setEditContent(message.content.text || "");
                    }}
                    className="text-gray-400 hover:text-green-500 p-1 rounded transition-colors"
                    title="Edit message"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={() => onDelete?.(message._id)}
                    className="text-gray-400 hover:text-red-500 p-1 rounded transition-colors"
                    title="Delete message"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </button>
                </>
              )}

              {!isOwnMessage && (
                <button
                  onClick={() => onReport?.(message._id)}
                  className="text-gray-400 hover:text-red-500 p-1.5 rounded-lg hover:bg-red-50 transition-all duration-200 hover:scale-110"
                  title="Report message"
                >
                  <Flag className="w-4 h-4" />
                </button>
              )}

              <button
                className="text-gray-400 hover:text-gray-600 p-1.5 rounded-lg hover:bg-gray-50 transition-all duration-200 hover:scale-110"
                title="More options"
              >
                <MoreHorizontal className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>

        {/* Reactions Display - Attached to Message */}
        {(message.reactions || []).length > 0 && (
          <div
            className={`flex flex-wrap gap-1.5 mt-2 ${
              isOwnMessage ? "justify-end" : "justify-start"
            }`}
          >
            {(message.reactions || []).map((reaction) => {
              const userHasReacted = hasUserReacted(reaction);
              return (
                <button
                  key={reaction.emoji}
                  onClick={() => onReaction?.(message._id, reaction.emoji)}
                  className={`inline-flex items-center space-x-1 rounded-full px-2.5 py-1 text-xs transition-all duration-200 hover:scale-105 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 ${
                    userHasReacted
                      ? "bg-primary-100 border-2 border-primary-300 text-primary-800 hover:bg-primary-200"
                      : "bg-white hover:bg-gray-50 border border-gray-200"
                  }`}
                  title={`${reaction.count} ${
                    reaction.count === 1 ? "person" : "people"
                  } reacted with ${reaction.emoji}${
                    userHasReacted ? " (including you)" : ""
                  }`}
                >
                  <span className="text-base leading-none">
                    {reaction.emoji}
                  </span>
                  <span
                    className={`font-semibold text-xs min-w-[1rem] text-center ${
                      userHasReacted ? "text-primary-800" : "text-gray-700"
                    }`}
                  >
                    {reaction.count}
                  </span>
                </button>
              );
            })}
          </div>
        )}

        {/* Compact Reaction Picker */}
        {showReactions && (
          <div
            ref={reactionPickerRef}
            className={`absolute z-[9999] mt-1 ${
              isOwnMessage ? "right-0" : "left-0"
            }`}
            style={{ zIndex: 9999 }}
          >
            <div className="bg-white border border-gray-200 rounded-lg shadow-xl p-3 ring-1 ring-black ring-opacity-5 animate-slide-up">
              <div className="flex space-x-2">
                {commonReactions.map((emoji) => (
                  <button
                    key={emoji}
                    onClick={() => {
                      onReaction?.(message._id, emoji);
                      setShowReactions(false);
                    }}
                    className="hover:bg-gray-100 p-2 rounded-lg text-xl transition-all duration-200 hover:scale-110 active:scale-95 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:bg-gray-100"
                    title={`React with ${emoji}`}
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
