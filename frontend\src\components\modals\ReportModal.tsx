"use client";

import { useState, useEffect } from "react";
import { XMarkIcon, FlagIcon } from "@heroicons/react/24/outline";

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (reason: string, category: string) => void;
  messageId: string;
  isLoading?: boolean;
}

export default function ReportModal({
  isOpen,
  onClose,
  onSubmit,
  messageId,
  isLoading = false,
}: ReportModalProps) {
  const [selectedCategory, setSelectedCategory] = useState(
    "inappropriate_content"
  );
  const [customReason, setCustomReason] = useState("");

  const reportCategories = [
    {
      id: "inappropriate_content",
      label: "Inappropriate Content",
      description: "Contains offensive or inappropriate material",
    },
    {
      id: "spam",
      label: "Spam",
      description: "Repetitive or unwanted content",
    },
    {
      id: "harassment",
      label: "Harassment",
      description: "Bullying, threats, or harassment",
    },
    {
      id: "fake_profile",
      label: "Fake Profile",
      description: "Impersonation or fake account",
    },
    {
      id: "unprofessional_conduct",
      label: "Unprofessional Conduct",
      description: "Inappropriate professional behavior",
    },
    {
      id: "technical_issue",
      label: "Technical Issue",
      description: "Platform or technical problems",
    },
    {
      id: "billing_issue",
      label: "Billing Issue",
      description: "Payment or billing related concerns",
    },
    {
      id: "privacy_concern",
      label: "Privacy Concern",
      description: "Privacy or data protection issues",
    },
    {
      id: "safety_concern",
      label: "Safety Concern",
      description: "Safety or security related issues",
    },
    {
      id: "other",
      label: "Other",
      description: "Other reason not listed above",
    },
  ];

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!customReason.trim()) {
      return;
    }

    onSubmit(customReason.trim(), selectedCategory);
  };

  const handleClose = () => {
    if (!isLoading) {
      setSelectedCategory("inappropriate_content");
      setCustomReason("");
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4 text-center">
        <div className="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <FlagIcon className="h-6 w-6 text-red-500" />
              <h3 className="text-lg font-semibold leading-6 text-gray-900">
                Report Message
              </h3>
            </div>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <p className="text-sm text-gray-600 mb-6">
            Help us keep our community safe by reporting inappropriate content.
            Your report will be reviewed by our moderation team.
          </p>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Category Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Reason for reporting
              </label>
              <div className="space-y-2">
                {reportCategories.map((category) => (
                  <label
                    key={category.id}
                    className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <input
                      type="radio"
                      name="category"
                      value={category.id}
                      checked={selectedCategory === category.id}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="mt-0.5 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                      disabled={isLoading}
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900">
                        {category.label}
                      </div>
                      <div className="text-xs text-gray-500">
                        {category.description}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Custom Reason */}
            <div>
              <label
                htmlFor="reason"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Additional details <span className="text-red-500">*</span>
              </label>
              <textarea
                id="reason"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                placeholder="Please provide specific details about why you're reporting this message..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                disabled={isLoading}
                required
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || !customReason.trim()}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Reporting...</span>
                  </div>
                ) : (
                  "Submit Report"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
