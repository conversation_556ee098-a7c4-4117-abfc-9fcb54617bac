import Link from "next/link";
import { Heart, Mail, Phone, MapPin } from "lucide-react";

const footerLinks = {
  platform: [
    { name: "Find Counselors", href: "/counselors" },
    { name: "Chat Rooms", href: "/chatrooms" },
    { name: "Resources", href: "/resources" },
    { name: "How It Works", href: "/how-it-works" },
  ],
  support: [
    { name: "Help Center", href: "/help" },
    { name: "Crisis Support", href: "/crisis" },
    { name: "Contact Us", href: "/contact" },
    { name: "FAQs", href: "/faq" },
  ],
  legal: [
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Terms of Service", href: "/terms" },
    { name: "HIPAA Compliance", href: "/hipaa" },
    { name: "Code of Ethics", href: "/ethics" },
  ],
};

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 sm:gap-8">
          {/* Brand */}
          <div className="sm:col-span-2 lg:col-span-2">
            <div className="flex items-center mb-4">
              <Heart className="h-7 w-7 sm:h-8 sm:w-8 text-purple-400 mr-3" />
              <span className="text-xl sm:text-2xl font-bold">Theramea</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md text-sm sm:text-base leading-relaxed">
              Your trusted platform for mental health support. Connect with
              licensed counselors and find the help you need.
            </p>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300 text-sm sm:text-base">
                <Phone className="h-4 w-4 mr-3 flex-shrink-0" />
                <span>1-800-THERAPY</span>
              </div>
              <div className="flex items-center text-gray-300 text-sm sm:text-base">
                <Mail className="h-4 w-4 mr-3 flex-shrink-0" />
                <span className="break-all sm:break-normal">
                  <EMAIL>
                </span>
              </div>
              <div className="flex items-center text-gray-300 text-sm sm:text-base">
                <MapPin className="h-4 w-4 mr-3 flex-shrink-0" />
                <span>Available Nationwide</span>
              </div>
            </div>
          </div>

          {/* Platform Links */}
          <div className="mt-8 sm:mt-0">
            <h3 className="font-semibold mb-4 text-base sm:text-lg">
              Platform
            </h3>
            <ul className="space-y-3">
              {footerLinks.platform.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm sm:text-base touch-manipulation block py-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div className="mt-8 sm:mt-0">
            <h3 className="font-semibold mb-4 text-base sm:text-lg">Support</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm sm:text-base touch-manipulation block py-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div className="mt-8 sm:mt-0">
            <h3 className="font-semibold mb-4 text-base sm:text-lg">Legal</h3>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm sm:text-base touch-manipulation block py-1"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 sm:mt-12 pt-6 sm:pt-8">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <p className="text-gray-400 text-xs sm:text-sm text-center sm:text-left">
              © 2025 Theramea. All rights reserved.
            </p>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-center sm:text-left">
              <p className="text-gray-400 text-xs sm:text-sm flex items-center justify-center sm:justify-start">
                <span className="mr-1">🔒</span> HIPAA Compliant
              </p>
              <p className="text-gray-400 text-xs sm:text-sm flex items-center justify-center sm:justify-start">
                <span className="mr-1">✓</span> Secure & Encrypted
              </p>
              <p className="text-gray-400 text-xs sm:text-sm flex items-center justify-center sm:justify-start">
                <span className="mr-1">📞</span> 24/7 Crisis Support
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
