import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import mongoose from "mongoose";
import { ChatService } from "@/services/chatService";
import { ModerationService } from "@/services/moderationService";
import { createError } from "@/middleware/errorHandler";
import { validateFile, FILE_TYPES } from "@/utils/cloudinary";

export class ChatController {
  /**
   * Get chat rooms list
   */
  static async getChatRooms(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "statistics.lastActivityAt",
        sortOrder = "desc",
        topic,
        category,
        tags,
        search,
      } = req.query;

      const filters: any = {};
      if (topic) filters.topic = topic;
      if (category) filters.category = category;
      if (tags) filters.tags = Array.isArray(tags) ? tags : [tags];
      if (search) filters.search = search;

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        sortBy: sortBy as string,
        sortOrder: sortOrder as "asc" | "desc",
      };

      const result = await ChatService.getChatRooms(filters, options);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get specific chat room
   */
  static async getChatRoom(req: Request, res: Response, next: NextFunction) {
    try {
      const { roomId } = req.params;
      const chatRoom = await ChatService.getChatRoom(roomId);

      res.json({
        success: true,
        data: { chatRoom },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new chat room (admin/moderator only)
   */
  static async createChatRoom(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError("Validation failed", 400));
      }

      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const chatRoom = await ChatService.createChatRoom(
        req.user._id.toString(),
        req.body
      );

      res.status(201).json({
        success: true,
        message: "Chat room created successfully",
        data: { chatRoom },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Join a chat room
   */
  static async joinChatRoom(req: Request, res: Response, next: NextFunction) {
    try {
      const { roomId } = req.params;
      const { displayName, anonymousId } = req.body;

      // Check if user is authenticated and has a valid MongoDB ObjectId
      const isAuthenticated =
        req.user && req.user._id && mongoose.isValidObjectId(req.user._id);

      // Debug logging
      console.log("DEBUG - Join Chat Room Request:", {
        roomId,
        displayName,
        anonymousId,
        isAuthenticated,
        userId: req.user?._id,
        userRole: req.user?.role,
        headers: {
          authorization: req.headers.authorization ? "present" : "absent",
        },
      });

      // Generate a proper display name if none provided or if it's invalid
      let finalDisplayName = displayName;
      if (
        !finalDisplayName ||
        finalDisplayName.trim() === "" ||
        finalDisplayName === "undefined undefined" ||
        finalDisplayName.includes("undefined")
      ) {
        if (isAuthenticated && req.user?.firstName && req.user?.lastName) {
          finalDisplayName = `${req.user.firstName} ${req.user.lastName}`;
        } else {
          // Generate anonymous display name for guests or users without names
          finalDisplayName = ChatService.generateAnonymousDisplayName();
        }
      }

      const joinData = {
        userId: isAuthenticated ? req.user!._id.toString() : undefined,
        anonymousId: !isAuthenticated
          ? anonymousId || req.user?._id?.toString()
          : undefined,
        displayName: finalDisplayName,
      };

      console.log("DEBUG - Join Data being sent to service:", joinData);

      const chatRoom = await ChatService.joinChatRoom(roomId, joinData);

      res.json({
        success: true,
        message: "Successfully joined chat room",
        data: { chatRoom },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get messages from a chat room
   */
  static async getMessages(req: Request, res: Response, next: NextFunction) {
    try {
      const { roomId } = req.params;
      const { page = 1, limit = 50, before, after } = req.query;

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        before: before as string,
        after: after as string,
      };

      const result = await ChatService.getMessages(roomId, options);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Upload file for chat
   */
  static async uploadChatFile(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.file) {
        return next(createError("File is required", 400));
      }

      // Validate file
      const allowedTypes = [
        ...FILE_TYPES.IMAGES,
        ...FILE_TYPES.DOCUMENTS,
        ...FILE_TYPES.AUDIO,
      ];
      validateFile(req.file, allowedTypes, 5 * 1024 * 1024); // 5MB limit

      const fileUrl = await ChatService.uploadChatFile(req.file);

      res.json({
        success: true,
        message: "File uploaded successfully",
        data: {
          fileUrl,
          fileName: req.file.originalname,
          fileSize: req.file.size,
          mimeType: req.file.mimetype,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate anonymous credentials
   */
  static async generateAnonymousCredentials(
    _req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const anonymousId = ChatService.generateAnonymousId();
      const displayName = ChatService.generateAnonymousDisplayName();

      res.json({
        success: true,
        data: {
          anonymousId,
          displayName,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Report a message or user
   */
  static async reportContent(req: Request, res: Response, next: NextFunction) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return next(createError("Validation failed", 400));
      }

      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { targetId, targetType, reason, description, evidence } = req.body;

      await ModerationService.reportContent({
        reportedBy: req.user._id.toString(),
        targetId,
        targetType,
        reason,
        description,
        evidence,
      });

      res.json({
        success: true,
        message:
          "Content reported successfully. Our moderation team will review it.",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Hide message (moderator only)
   */
  static async hideMessage(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { messageId } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return next(createError("Reason is required", 400));
      }

      await ModerationService.hideMessage(
        messageId,
        req.user._id.toString(),
        reason
      );

      res.json({
        success: true,
        message: "Message hidden successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete message (moderator only)
   */
  static async deleteMessage(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { messageId } = req.params;

      await ModerationService.deleteMessage(messageId, req.user._id.toString());

      res.json({
        success: true,
        message: "Message deleted successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mute user in chat room (moderator only)
   */
  static async muteUser(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { roomId, userId } = req.params;
      const { duration, reason } = req.body;

      await ModerationService.muteUser(
        roomId,
        userId,
        req.user._id.toString(),
        duration,
        reason
      );

      res.json({
        success: true,
        message: "User muted successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Unmute user in chat room (moderator only)
   */
  static async unmuteUser(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { roomId, userId } = req.params;

      await ModerationService.unmuteUser(
        roomId,
        userId,
        req.user._id.toString()
      );

      res.json({
        success: true,
        message: "User unmuted successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get moderation queue (admin only)
   */
  static async getModerationQueue(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const {
        page = 1,
        limit = 20,
        status = "pending",
        priority,
        reportType,
      } = req.query;

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        status: status as string,
        priority: priority as string,
        reportType: reportType as string,
      };

      const result = await ModerationService.getModerationQueue(options);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Resolve report (admin only)
   */
  static async resolveReport(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { reportId } = req.params;
      const { action, description } = req.body;

      if (!action || !description) {
        return next(createError("Action and description are required", 400));
      }

      await ModerationService.resolveReport(
        reportId,
        req.user._id.toString(),
        action,
        description
      );

      res.json({
        success: true,
        message: "Report resolved successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's moderation history (admin only)
   */
  static async getUserModerationHistory(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { userId } = req.params;

      const history = await ModerationService.getUserModerationHistory(userId);

      res.json({
        success: true,
        data: history,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mark messages as read in a room
   */
  static async markMessagesAsRead(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { roomId } = req.params;
      const user = req.user;
      const anonymousId = req.headers["x-anonymous-id"] as string;

      await ChatService.markMessagesAsRead(roomId, user?.id, anonymousId);

      res.json({
        success: true,
        message: "Messages marked as read",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get unread message count for a room
   */
  static async getUnreadMessageCount(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { roomId } = req.params;
      const user = req.user;
      const anonymousId = req.headers["x-anonymous-id"] as string;

      const count = await ChatService.getUnreadMessageCount(
        roomId,
        user?.id,
        anonymousId
      );

      res.json({
        success: true,
        data: { unreadCount: count },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Edit user's own message
   */
  static async editOwnMessage(req: Request, res: Response, next: NextFunction) {
    try {
      const { messageId } = req.params;
      const { content } = req.body;
      const user = req.user;
      const anonymousId = req.headers["x-anonymous-id"] as string;

      await ChatService.editOwnMessage(
        messageId,
        content,
        user?.id,
        anonymousId
      );

      res.json({
        success: true,
        message: "Message edited successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete user's own message
   */
  static async deleteOwnMessage(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { messageId } = req.params;
      const user = req.user;
      const anonymousId = req.headers["x-anonymous-id"] as string;

      await ChatService.deleteOwnMessage(
        messageId,
        user?._id.toString(),
        anonymousId
      );

      res.json({
        success: true,
        message: "Message deleted successfully",
      });
    } catch (error) {
      next(error);
    }
  }
}
