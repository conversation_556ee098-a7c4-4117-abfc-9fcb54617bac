import { Request, Response, NextFunction } from "express";
import { AdminService } from "@/services/adminService";
import { createError } from "@/utils/errorHandler";

export class AdminController {
  /**
   * Get dashboard statistics
   */
  static async getDashboardStats(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return res
          .status(401)
          .json({ success: false, message: "Authentication required" });
      }

      const stats = await AdminService.getDashboardStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get platform metrics
   */
  static async getPlatformMetrics(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { period = "month" } = req.query;
      const metrics = await AdminService.getPlatformMetrics(period as string);

      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all reports
   */
  static async getReports(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { page = 1, limit = 20, status, priority, type } = req.query;

      const reports = await AdminService.getReports({
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        status: status as string,
        priority: priority as string,
        type: type as string,
      });

      res.json({
        success: true,
        data: reports,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update report status
   */
  static async updateReportStatus(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { reportId } = req.params;
      const { status, resolution } = req.body;

      const report = await AdminService.updateReportStatus(
        reportId,
        status,
        resolution,
        req.user._id.toString()
      );

      res.json({
        success: true,
        data: report,
        message: "Report status updated successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get admin actions log
   */
  static async getAdminActions(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { page = 1, limit = 20, adminId, action, targetType } = req.query;

      const actions = await AdminService.getAdminActions({
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        adminId: adminId as string,
        action: action as string,
        targetType: targetType as string,
      });

      res.json({
        success: true,
        data: actions,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get system settings
   */
  static async getSystemSettings(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const settings = await AdminService.getSystemSettings();

      res.json({
        success: true,
        data: settings,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update system settings
   */
  static async updateSystemSettings(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const settings = await AdminService.updateSystemSettings(
        req.body,
        req.user._id.toString()
      );

      res.json({
        success: true,
        data: settings,
        message: "System settings updated successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Suspend user
   */
  static async suspendUser(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { userId } = req.params;
      const { reason, duration } = req.body;

      await AdminService.suspendUser(
        userId,
        reason,
        duration,
        req.user._id.toString()
      );

      res.json({
        success: true,
        message: "User suspended successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Suspend counselor
   */
  static async suspendCounselor(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      if (!req.user) {
        return next(createError("Authentication required", 401));
      }

      const { counselorId } = req.params;
      const { reason } = req.body;

      await AdminService.suspendCounselor(
        counselorId,
        reason,
        req.user._id.toString()
      );

      res.json({
        success: true,
        message: "Counselor suspended successfully",
      });
    } catch (error) {
      next(error);
    }
  }
}
