// Chat and chatroom types

export interface ChatRoom {
  _id: string;
  name: string;
  description: string;
  topic: string;
  category:
    | "support-groups"
    | "educational"
    | "peer-chat"
    | "crisis-support"
    | "special-topics";
  isActive: boolean;
  isModerated: boolean;
  maxParticipants: number;
  currentParticipants: number;
  moderators: string[];
  participants: ChatParticipant[];
  settings: {
    allowAnonymous: boolean;
    requireApproval: boolean;
    allowFileSharing: boolean;
    messageRetentionDays: number;
    slowModeDelay: number;
    profanityFilter: boolean;
  };
  rules: string[];
  tags: string[];
  statistics: {
    totalMessages: number;
    totalParticipants: number;
    averageSessionDuration: number;
    peakParticipants: number;
    lastActivityAt: string;
  };
  schedule: {
    isScheduled: boolean;
    startTime?: string;
    endTime?: string;
    recurringPattern?: "daily" | "weekly" | "monthly";
    timezone: string;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ChatParticipant {
  userId?: string;
  anonymousId?: string;
  joinedAt: string;
  lastSeen: string;
  isOnline: boolean;
  role: "participant" | "moderator" | "readonly";
  displayName: string;
  isMuted: boolean;
  mutedUntil?: string;
}

export interface ChatMessage {
  _id: string;
  chatRoomId: string;
  senderId?: string;
  anonymousSenderId?: string;
  senderDisplayName: string;
  content: {
    text?: string;
    type: "text" | "image" | "file" | "system" | "emoji";
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
  };
  mentions: string[];
  replyTo?: string;
  reactions: {
    emoji: string;
    users: {
      userId?: string;
      anonymousId?: string;
      addedAt: string;
    }[];
    count: number;
  }[];
  isEdited: boolean;
  editHistory: {
    previousContent: string;
    editedAt: string;
  }[];
  status?: "sending" | "sent" | "delivered" | "read" | "failed";
  moderation: {
    isReported: boolean;
    reportCount: number;
    reports: {
      reportedBy: string;
      reason:
        | "inappropriate_content"
        | "harassment"
        | "spam"
        | "fake_profile"
        | "unprofessional_conduct"
        | "technical_issue"
        | "billing_issue"
        | "privacy_concern"
        | "safety_concern"
        | "other";
      description?: string;
      reportedAt: string;
    }[];
    isHidden: boolean;
    hiddenBy?: string;
    hiddenAt?: string;
    hiddenReason?: string;
    isDeleted: boolean;
    deletedBy?: string;
    deletedAt?: string;
  };
  metadata: {
    ipAddress?: string;
    userAgent?: string;
    location?: string;
    deviceType?: "mobile" | "desktop" | "tablet";
  };
  readBy: {
    userId?: string;
    anonymousId?: string;
    readAt: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface ChatRoomFilters {
  topic?: string;
  category?: string;
  tags?: string[];
  search?: string;
}

export interface ChatRoomListOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface TypingUser {
  userId?: string;
  anonymousId?: string;
  displayName: string;
}

export interface SendMessageData {
  content: {
    text?: string;
    type: "text" | "image" | "file" | "emoji";
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
  };
  replyTo?: string;
  mentions?: string[];
}

export interface JoinRoomData {
  userId?: string;
  anonymousId?: string;
  displayName: string;
}

// Chat categories with display information
export const CHAT_CATEGORIES = [
  {
    value: "support-groups",
    label: "Support Groups",
    description: "Peer support and shared experiences",
    icon: "🤝",
    color: "blue",
  },
  {
    value: "educational",
    label: "Educational",
    description: "Learning and skill development",
    icon: "📚",
    color: "green",
  },
  {
    value: "peer-chat",
    label: "Peer Chat",
    description: "Casual conversations and connections",
    icon: "💬",
    color: "purple",
  },
  {
    value: "crisis-support",
    label: "Crisis Support",
    description: "Immediate support and resources",
    icon: "🆘",
    color: "red",
  },
  {
    value: "special-topics",
    label: "Special Topics",
    description: "Focused discussions on specific themes",
    icon: "🎯",
    color: "yellow",
  },
] as const;

export type ChatCategory = (typeof CHAT_CATEGORIES)[number]["value"];
