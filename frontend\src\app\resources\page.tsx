"use client";

import { useState, useEffect } from "react";
import Header from "@/components/layout/Header";
import Link from "next/link";
import ResourceCard from "@/components/resources/ResourceCard";
import { Resource as ResourceType } from "@/types/resources";

export default function ResourcesPage() {
  const [resources, setResources] = useState<ResourceType[]>([]);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("popular");
  const [isLoading, setIsLoading] = useState(true);

  const categories = [
    { id: "all", name: "All Categories", icon: "📚" },
    { id: "self-care", name: "Self-Care", icon: "🌱" },
    { id: "mindfulness", name: "Mindfulness & Meditation", icon: "🧘" },
    { id: "sleep", name: "Sleep & Relaxation", icon: "😴" },
    { id: "mental-health", name: "Mental Health", icon: "🧠" },
    { id: "anxiety", name: "Anxiety & Stress", icon: "😰" },
    { id: "depression", name: "Depression Support", icon: "💙" },
    { id: "stress-relief", name: "Stress Relief", icon: "😌" },
    { id: "relationships", name: "Relationships", icon: "💕" },
    { id: "coping-strategies", name: "Coping Strategies", icon: "🛠️" },
    { id: "crisis-resources", name: "Crisis Resources", icon: "🆘" },
    { id: "workplace-wellness", name: "Workplace Wellness", icon: "💼" },
    { id: "student-support", name: "Student Support", icon: "🎓" },
    { id: "parenting", name: "Parenting", icon: "👨‍👩‍👧‍👦" },
    { id: "grief-support", name: "Grief & Loss", icon: "🕊️" },
    { id: "addiction-recovery", name: "Addiction Recovery", icon: "🔄" },
    { id: "trauma-healing", name: "Trauma & PTSD", icon: "🛡️" },
  ];

  const resourceTypes = [
    { id: "all", name: "All Types" },
    { id: "article", name: "Articles" },
    { id: "video", name: "Videos" },
    { id: "audio", name: "Audio" },
    { id: "worksheet", name: "Worksheets" },
    { id: "guide", name: "Guides" },
    { id: "tool", name: "Tools" },
  ];

  useEffect(() => {
    fetchResources();
  }, []);

  const fetchResources = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("http://localhost:5000/api/resources");

      if (!response.ok) {
        throw new Error("Failed to fetch resources");
      }

      const data = await response.json();

      if (data.success && data.data.content) {
        // Validate and clean the data to ensure all required fields exist
        const validatedResources = data.data.content
          .filter((resource: any) => resource && resource._id && resource.title)
          .map((resource: any) => ({
            ...resource,
            // Ensure author object exists with at least a name
            author: {
              name: resource.author?.name || "Unknown Author",
              credentials: resource.author?.credentials || "",
              bio: resource.author?.bio || "",
            },
            // Ensure seo object exists with slug
            seo: {
              slug:
                resource.seo?.slug || resource.seoData?.slug || resource._id,
              metaTitle: resource.seo?.metaTitle || resource.title,
              metaDescription:
                resource.seo?.metaDescription || resource.description,
              keywords: resource.seo?.keywords || [],
            },
            // Map engagement to statistics to match Resource type
            statistics: {
              views:
                resource.engagement?.views || resource.statistics?.views || 0,
              likes: resource.statistics?.likes || 0,
              bookmarks: resource.statistics?.bookmarks || 0,
              shares: resource.statistics?.shares || 0,
              averageRating:
                resource.engagement?.averageRating ||
                resource.statistics?.averageRating ||
                0,
              totalRatings:
                resource.engagement?.totalRatings ||
                resource.statistics?.totalRatings ||
                0,
              completionRate: resource.statistics?.completionRate || 0,
            },
            // Add required fields
            content: resource.content || "",
            interactions: resource.interactions || [],
            ratings: resource.ratings || [],
            bookmarkedBy: resource.bookmarkedBy || [],
            likedBy: resource.likedBy || [],
            // Ensure required fields have fallbacks
            title: resource.title || "Untitled Resource",
            description: resource.description || "No description available",
            category: resource.category || "General",
            type: resource.type || "article",
            difficulty: resource.difficulty || "beginner",
            tags: Array.isArray(resource.tags) ? resource.tags : [],
            publishedAt: resource.publishedAt || new Date().toISOString(),
            isPublished: resource.isPublished !== false,
            isFeatured: resource.isFeatured === true,
          }));

        setResources(validatedResources);
        console.log("📊 Loaded resources with categories:", [
          ...new Set(validatedResources.map((r: any) => r.category)),
        ]);
      } else {
        console.error("Invalid API response format:", data);
        setResources([]);
      }
    } catch (error) {
      console.error("Error fetching resources:", error);
      setResources([]);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredResources = resources.filter((resource) => {
    const matchesCategory =
      selectedCategory === "all" || resource.category === selectedCategory;
    const matchesType =
      selectedType === "all" || resource.type === selectedType;
    const matchesSearch =
      resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase())
      );

    // Debug logging
    if (selectedCategory !== "all") {
      console.log(
        `🔍 Filtering: selectedCategory="${selectedCategory}", resource.category="${resource.category}", matches=${matchesCategory}`
      );
    }

    return matchesCategory && matchesType && matchesSearch;
  });

  const sortedResources = [...filteredResources].sort((a, b) => {
    switch (sortBy) {
      case "popular":
        return (b.statistics?.views || 0) - (a.statistics?.views || 0);
      case "rating":
        return (
          (b.statistics?.averageRating || 0) -
          (a.statistics?.averageRating || 0)
        );
      case "newest":
        return (
          new Date(b.publishedAt || 0).getTime() -
          new Date(a.publishedAt || 0).getTime()
        );
      case "title":
        return a.title.localeCompare(b.title);
      default:
        return 0;
    }
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            Mental Health Resources
          </h1>
          <p className="text-sm sm:text-base text-gray-600">
            Discover evidence-based tools, guides, and content to support your
            mental health journey.
          </p>
        </div>

        {/* Featured Resource Banner */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 text-white">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs font-medium">
                  Featured
                </span>
                <span className="text-purple-200">🌟</span>
              </div>
              <h2 className="text-xl sm:text-2xl font-bold mb-2">
                New: Anxiety Management Toolkit
              </h2>
              <p className="text-sm sm:text-base text-purple-100 mb-4">
                A comprehensive collection of techniques and exercises to help
                manage anxiety in daily life.
              </p>
              <Link
                href="/resources/anxiety-toolkit"
                className="bg-white text-purple-600 px-6 py-3 rounded-lg font-medium hover:bg-purple-50 transition-colors touch-manipulation inline-block"
              >
                Explore Toolkit
              </Link>
            </div>
            <div className="hidden lg:block ml-8">
              <div className="w-32 h-32 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                <span className="text-6xl">🧠</span>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1 order-2 lg:order-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 lg:sticky lg:top-8">
              <h3 className="font-semibold text-gray-900 mb-4 text-sm sm:text-base">
                Filters
              </h3>

              {/* Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Resources
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-base touch-manipulation"
                  />
                  <svg
                    className="absolute left-3 top-3.5 h-5 w-5 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
              </div>

              {/* Categories */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <div className="space-y-1 max-h-64 overflow-y-auto">
                  {categories.slice(0, 8).map((category) => (
                    <button
                      key={category.id}
                      onClick={() => {
                        console.log(`🎯 Category selected: ${category.id}`);
                        setSelectedCategory(category.id);
                      }}
                      className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-colors touch-manipulation ${
                        selectedCategory === category.id
                          ? "bg-purple-100 text-purple-700"
                          : "hover:bg-gray-50 text-gray-700"
                      }`}
                    >
                      <span className="text-lg flex-shrink-0">
                        {category.icon}
                      </span>
                      <span className="text-sm font-medium truncate">
                        {category.name}
                      </span>
                    </button>
                  ))}
                  {categories.length > 8 && (
                    <button className="w-full text-sm text-purple-600 hover:text-purple-700 py-2 text-center">
                      Show {categories.length - 8} more categories
                    </button>
                  )}
                </div>
              </div>

              {/* Resource Type */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Resource Type
                </label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-3 focus:ring-2 focus:ring-purple-500 focus:border-transparent text-base touch-manipulation"
                >
                  {resourceTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3 text-sm sm:text-base">
                  Quick Access
                </h4>
                <div className="space-y-2">
                  <Link
                    href="/resources/favorites"
                    className="flex items-center space-x-2 text-sm text-gray-600 hover:text-purple-600 py-2 touch-manipulation"
                  >
                    <svg
                      className="w-4 h-4 flex-shrink-0"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                    <span>My Favorites</span>
                  </Link>
                  <Link
                    href="/resources/bookmarks"
                    className="flex items-center space-x-2 text-sm text-gray-600 hover:text-purple-600 py-2 touch-manipulation"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                      />
                    </svg>
                    <span>Bookmarked</span>
                  </Link>
                  <Link
                    href="/resources/history"
                    className="flex items-center space-x-2 text-sm text-gray-600 hover:text-purple-600"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span>Recently Viewed</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Sort and Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {sortedResources.length} Resource
                  {sortedResources.length !== 1 ? "s" : ""} Found
                </h2>
                {selectedCategory !== "all" && (
                  <p className="text-sm text-gray-600">
                    in {categories.find((c) => c.id === selectedCategory)?.name}
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="popular">Most Popular</option>
                  <option value="rating">Highest Rated</option>
                  <option value="newest">Newest First</option>
                  <option value="title">Alphabetical</option>
                </select>
              </div>
            </div>

            {/* Resources Grid */}
            {isLoading ? (
              <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="bg-white rounded-lg shadow-sm border border-gray-200"
                  >
                    <div className="w-full h-48 bg-gray-200 rounded-t-lg animate-pulse"></div>
                    <div className="p-6">
                      <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                      <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
                      <div className="h-4 bg-gray-200 rounded mb-4 animate-pulse"></div>
                      <div className="flex gap-2 mb-4">
                        <div className="h-6 w-16 bg-gray-200 rounded-full animate-pulse"></div>
                        <div className="h-6 w-20 bg-gray-200 rounded-full animate-pulse"></div>
                      </div>
                      <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : sortedResources.length > 0 ? (
              <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-6">
                {sortedResources.map((resource) => (
                  <ResourceCard
                    key={resource._id}
                    resource={resource}
                    onBookmark={(resourceId, isBookmarked) => {
                      // Handle bookmark state change if needed
                      console.log(
                        `Resource ${resourceId} ${
                          isBookmarked ? "bookmarked" : "unbookmarked"
                        }`
                      );
                    }}
                    onLike={(resourceId, isLiked) => {
                      // Handle like state change if needed
                      console.log(
                        `Resource ${resourceId} ${
                          isLiked ? "liked" : "unliked"
                        }`
                      );
                    }}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No resources found
                </h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your filters or search terms to find what you're
                  looking for.
                </p>
                <button
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedCategory("all");
                    setSelectedType("all");
                  }}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
