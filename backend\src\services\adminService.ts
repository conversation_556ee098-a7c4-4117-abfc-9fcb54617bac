import mongoose from "mongoose";
import { User } from "@/models/User";
import { Counselor } from "@/models/Counselor";
import { Session } from "@/models/Session";
import { ChatRoom } from "@/models/ChatRoom";
import { AdminAction, SystemSettings, Report } from "@/models/Admin";
import { createError } from "@/utils/errorHandler";
import { logger } from "@/utils/logger";

export class AdminService {
  /**
   * Get dashboard statistics
   */
  static async getDashboardStats() {
    try {
      const [
        totalUsers,
        activeUsers,
        totalCounselors,
        approvedCounselors,
        pendingCounselors,
        totalSessions,
        completedSessions,
        totalRevenue,
        pendingReports,
        totalChatRooms,
        activeChatRooms,
      ] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ isActive: true }),
        Counselor.countDocuments(),
        Counselor.countDocuments({ "verification.status": "approved" }),
        Counselor.countDocuments({ "verification.status": "pending" }),
        Session.countDocuments(),
        Session.countDocuments({ status: "completed" }),
        Session.aggregate([
          { $match: { status: "completed" } },
          { $group: { _id: null, total: { $sum: "$price" } } },
        ]),
        Report.countDocuments({ status: "pending" }),
        ChatRoom.countDocuments(),
        ChatRoom.countDocuments({ isActive: true }),
      ]);

      // Calculate average counselor rating
      const ratingResult = await Counselor.aggregate([
        { $match: { "verification.status": "approved" } },
        { $group: { _id: null, avgRating: { $avg: "$rating.average" } } },
      ]);

      const averageRating = ratingResult[0]?.avgRating || 0;
      const revenue = totalRevenue[0]?.total || 0;

      return {
        users: {
          total: totalUsers,
          active: activeUsers,
          growth: 0, // TODO: Calculate growth percentage
        },
        counselors: {
          total: totalCounselors,
          approved: approvedCounselors,
          pending: pendingCounselors,
          averageRating: averageRating,
        },
        sessions: {
          total: totalSessions,
          completed: completedSessions,
          revenue: revenue,
        },
        reports: {
          pending: pendingReports,
        },
        chatRooms: {
          total: totalChatRooms,
          active: activeChatRooms,
        },
      };
    } catch (error) {
      logger.error("Get dashboard stats error:", error);
      throw error;
    }
  }

  /**
   * Get platform metrics
   */
  static async getPlatformMetrics(period: string = "month") {
    try {
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case "week":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "quarter":
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case "year":
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default: // month
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const [userGrowth, sessionGrowth, revenueGrowth, recentActivity] =
        await Promise.all([
          User.countDocuments({ createdAt: { $gte: startDate } }),
          Session.countDocuments({ createdAt: { $gte: startDate } }),
          Session.aggregate([
            { $match: { createdAt: { $gte: startDate }, status: "completed" } },
            { $group: { _id: null, total: { $sum: "$price" } } },
          ]),
          AdminAction.find({ createdAt: { $gte: startDate } })
            .populate("adminId", "firstName lastName")
            .sort({ createdAt: -1 })
            .limit(10),
        ]);

      const revenue = revenueGrowth[0]?.total || 0;

      return {
        period,
        userGrowth,
        sessionGrowth,
        revenueGrowth: revenue,
        recentActivity: recentActivity.map((action) => ({
          id: action._id,
          description: `${action.action} - ${action.details.description}`,
          timestamp: action.createdAt,
          adminName:
            (action.adminId as any)?.firstName +
            " " +
            (action.adminId as any)?.lastName,
        })),
      };
    } catch (error) {
      logger.error("Get platform metrics error:", error);
      throw error;
    }
  }

  /**
   * Get reports with filtering
   */
  static async getReports(filters: {
    page: number;
    limit: number;
    status?: string;
    priority?: string;
    type?: string;
  }) {
    try {
      const { page, limit, status, priority, type } = filters;
      const skip = (page - 1) * limit;

      const query: any = {};
      if (status) query.status = status;
      if (priority) query.priority = priority;
      if (type) query.type = type;

      const [reports, total] = await Promise.all([
        Report.find(query)
          .populate("reportedBy", "firstName lastName email")
          .populate("assignedTo", "firstName lastName")
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        Report.countDocuments(query),
      ]);

      return {
        reports,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error("Get reports error:", error);
      throw error;
    }
  }

  /**
   * Update report status
   */
  static async updateReportStatus(
    reportId: string,
    status: string,
    resolution: any,
    adminId: string
  ) {
    try {
      const report = await Report.findById(reportId);
      if (!report) {
        throw createError("Report not found", 404);
      }

      report.status = status as any;
      if (resolution) {
        report.resolution = {
          ...resolution,
          resolvedBy: new mongoose.Types.ObjectId(adminId),
          resolvedAt: new Date(),
        };
      }

      await report.save();

      // Log admin action
      await this.logAdminAction({
        adminId: new mongoose.Types.ObjectId(adminId),
        action: "report_updated",
        targetType: "system",
        targetId: report._id,
        details: {
          description: `Updated report status to ${status}`,
          previousState: { status: report.status },
          newState: { status },
        },
        severity: "medium",
      });

      return report;
    } catch (error) {
      logger.error("Update report status error:", error);
      throw error;
    }
  }

  /**
   * Get admin actions log
   */
  static async getAdminActions(filters: {
    page: number;
    limit: number;
    adminId?: string;
    action?: string;
    targetType?: string;
  }) {
    try {
      const { page, limit, adminId, action, targetType } = filters;
      const skip = (page - 1) * limit;

      const query: any = {};
      if (adminId) query.adminId = adminId;
      if (action) query.action = action;
      if (targetType) query.targetType = targetType;

      const [actions, total] = await Promise.all([
        AdminAction.find(query)
          .populate("adminId", "firstName lastName email")
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit),
        AdminAction.countDocuments(query),
      ]);

      return {
        actions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      logger.error("Get admin actions error:", error);
      throw error;
    }
  }

  /**
   * Get system settings
   */
  static async getSystemSettings() {
    try {
      let settings = await SystemSettings.findOne();

      if (!settings) {
        // Create default settings if none exist
        settings = await SystemSettings.create({
          platform: {
            name: "Theramea",
            description: "Mental Health Support Platform",
            supportEmail: "<EMAIL>",
            maintenanceMode: false,
          },
          features: {
            chatRooms: true,
            videoSessions: true,
            resourceLibrary: true,
            moodTracking: true,
            notifications: true,
          },
          limits: {
            maxChatRoomsPerUser: 5,
            maxSessionsPerDay: 10,
            maxFileUploadSize: 10485760, // 10MB
            maxMessageLength: 1000,
          },
          security: {
            sessionTimeout: 3600000, // 1 hour
            maxLoginAttempts: 5,
            passwordMinLength: 8,
            requireEmailVerification: true,
          },
        });
      }

      return settings;
    } catch (error) {
      logger.error("Get system settings error:", error);
      throw error;
    }
  }

  /**
   * Update system settings
   */
  static async updateSystemSettings(updates: any, adminId: string) {
    try {
      let settings = await SystemSettings.findOne();

      if (!settings) {
        settings = new SystemSettings(updates);
      } else {
        Object.assign(settings, updates);
      }

      await settings.save();

      // Log admin action
      await this.logAdminAction({
        adminId: new mongoose.Types.ObjectId(adminId),
        action: "settings_updated",
        targetType: "system",
        details: {
          description: "System settings updated",
          newState: updates,
        },
        severity: "medium",
      });

      return settings;
    } catch (error) {
      logger.error("Update system settings error:", error);
      throw error;
    }
  }

  /**
   * Suspend user
   */
  static async suspendUser(
    userId: string,
    reason: string,
    duration: number,
    adminId: string
  ) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw createError("User not found", 404);
      }

      user.isActive = false;
      await user.save();

      // Log admin action
      await this.logAdminAction({
        adminId: new mongoose.Types.ObjectId(adminId),
        action: "user_suspended",
        targetType: "user",
        targetId: user._id,
        details: {
          description: `User suspended: ${reason}`,
          reason,
          metadata: { duration },
        },
        severity: "high",
      });

      logger.info(`User ${userId} suspended by admin ${adminId}`);
    } catch (error) {
      logger.error("Suspend user error:", error);
      throw error;
    }
  }

  /**
   * Suspend counselor
   */
  static async suspendCounselor(
    counselorId: string,
    reason: string,
    adminId: string
  ) {
    try {
      const counselor = await Counselor.findById(counselorId);
      if (!counselor) {
        throw createError("Counselor not found", 404);
      }

      counselor.verification.status = "suspended";
      await counselor.save();

      // Log admin action
      await this.logAdminAction({
        adminId: new mongoose.Types.ObjectId(adminId),
        action: "counselor_suspended",
        targetType: "counselor",
        targetId: counselor._id,
        details: {
          description: `Counselor suspended: ${reason}`,
          reason,
        },
        severity: "high",
      });

      logger.info(`Counselor ${counselorId} suspended by admin ${adminId}`);
    } catch (error) {
      logger.error("Suspend counselor error:", error);
      throw error;
    }
  }

  /**
   * Log admin action
   */
  static async logAdminAction(actionData: {
    adminId: mongoose.Types.ObjectId;
    action: string;
    targetType: string;
    targetId?: mongoose.Types.ObjectId;
    details: {
      description: string;
      previousState?: any;
      newState?: any;
      reason?: string;
      metadata?: any;
    };
    severity: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    try {
      const action = new AdminAction({
        ...actionData,
        ipAddress: actionData.ipAddress || "unknown",
        userAgent: actionData.userAgent || "unknown",
      });

      await action.save();
      logger.info(`Admin action logged: ${actionData.action}`);
    } catch (error) {
      logger.error("Log admin action error:", error);
      // Don't throw error to avoid breaking the main operation
    }
  }
}
