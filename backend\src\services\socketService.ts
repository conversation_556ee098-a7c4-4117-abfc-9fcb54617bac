import { Server as SocketIOServer } from "socket.io";
import { Server as HTTPServer } from "http";
import { JWTService } from "@/utils/jwt";
import { ChatService } from "@/services/chatService";
import { ModerationService } from "@/services/moderationService";
import { User } from "@/models/User";
import { logger } from "@/utils/logger";
import { getRedisClient } from "@/config/redis";

export interface SocketUser {
  id: string;
  userId?: string;
  anonymousId?: string;
  displayName: string;
  isAnonymous: boolean;
  currentRoom?: string;
}

export interface ChatMessage {
  id: string;
  roomId: string;
  senderId?: string;
  anonymousSenderId?: string;
  senderDisplayName: string;
  content: {
    text?: string;
    type: "text" | "image" | "file" | "emoji" | "system";
    fileUrl?: string;
    fileName?: string;
  };
  replyTo?: string;
  mentions?: string[];
  timestamp: Date;
}

export class SocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, SocketUser> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true,
      },
      transports: ["websocket", "polling"],
    });

    this.setupMiddleware();
    this.setupEventHandlers();
    logger.info("Socket.IO server initialized");
  }

  /**
   * Setup authentication middleware
   */
  private setupMiddleware() {
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        const anonymousId = socket.handshake.auth.anonymousId;
        const displayName = socket.handshake.auth.displayName;

        console.log("=== Socket Middleware Debug ===");
        console.log("Token received:", !!token);
        console.log("Anonymous ID:", anonymousId);
        console.log("Display name from handshake:", displayName);

        let user: SocketUser;

        if (token) {
          try {
            const decoded = JWTService.verifyAccessToken(token);
            console.log("Token decoded successfully:", decoded);
            const { userId, guestId, isGuest } = decoded as any;

            if (userId && !isGuest) {
              // Authenticated user
              const dbUser = await User.findById(userId);
              if (!dbUser) {
                throw new Error("User not found");
              }

              user = {
                id: socket.id,
                userId: userId.toString(),
                displayName: `${dbUser.firstName} ${dbUser.lastName}`,
                isAnonymous: false,
              };
              console.log("Authenticated user connected:", user);
            } else if (guestId || isGuest) {
              // Guest user with token
              console.log("Guest token processing:");
              console.log("Guest ID:", guestId);
              console.log("Is Guest:", isGuest);

              user = {
                id: socket.id,
                anonymousId: guestId || userId,
                displayName:
                  displayName || ChatService.generateAnonymousDisplayName(),
                isAnonymous: true,
              };

              console.log("Final user object:", user);
            } else {
              // Anonymous guest (shouldn't happen with valid token)
              user = {
                id: socket.id,
                anonymousId: anonymousId || ChatService.generateAnonymousId(),
                displayName:
                  displayName || ChatService.generateAnonymousDisplayName(),
                isAnonymous: true,
              };
            }
          } catch (error) {
            console.error("Token verification failed:", error);

            // Anonymous user
            user = {
              id: socket.id,
              anonymousId: anonymousId || ChatService.generateAnonymousId(),
              displayName:
                displayName || ChatService.generateAnonymousDisplayName(),
              isAnonymous: true,
            };
          }
        } else {
          // Anonymous user
          user = {
            id: socket.id,
            anonymousId: anonymousId || ChatService.generateAnonymousId(),
            displayName:
              displayName || ChatService.generateAnonymousDisplayName(),
            isAnonymous: true,
          };
        }

        socket.data.user = user;
        this.connectedUsers.set(socket.id, user);

        logger.info(
          `Socket connected: ${user.displayName} (${
            user.isAnonymous ? "anonymous" : "authenticated"
          })`
        );
        next();
      } catch (error) {
        logger.error("Socket authentication error:", error);
        next(new Error("Authentication failed"));
      }
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers() {
    this.io.on("connection", (socket) => {
      const user = socket.data.user as SocketUser;

      // Join room event
      socket.on("join-room", async (data: { roomId: string }) => {
        try {
          const { roomId } = data;

          // Leave current room if any
          if (user.currentRoom) {
            await this.leaveRoom(socket, user.currentRoom);
          }

          // Join new room
          const chatRoom = await ChatService.joinChatRoom(roomId, {
            userId: user.userId,
            anonymousId: user.anonymousId,
            displayName: user.displayName,
          });

          socket.join(roomId);
          user.currentRoom = roomId;
          this.connectedUsers.set(socket.id, user);

          // Store user's room in Redis for persistence
          try {
            const redisClient = getRedisClient();
            if (redisClient) {
              await redisClient.setEx(`user_room:${socket.id}`, 3600, roomId);
            }
          } catch (error) {
            logger.warn("Failed to store user room in Redis:", error);
          }

          // Notify room about new participant
          socket.to(roomId).emit("user-joined", {
            userId: user.userId,
            anonymousId: user.anonymousId,
            displayName: user.displayName,
            isAnonymous: user.isAnonymous,
            timestamp: new Date(),
          });

          // Send room info to user
          socket.emit("room-joined", {
            room: chatRoom,
            participantCount: chatRoom.currentParticipants,
          });

          logger.info(`User ${user.displayName} joined room ${roomId}`);
        } catch (error) {
          logger.error("Join room error:", error);
          socket.emit("error", {
            message:
              error instanceof Error ? error.message : "Failed to join room",
          });
        }
      });

      // Leave room event
      socket.on("leave-room", async (data: { roomId: string }) => {
        try {
          await this.leaveRoom(socket, data.roomId);
        } catch (error) {
          logger.error("Leave room error:", error);
          socket.emit("error", { message: "Failed to leave room" });
        }
      });

      // Send message event
      socket.on(
        "send-message",
        async (data: {
          roomId: string;
          content: {
            text?: string;
            type: "text" | "image" | "file" | "emoji";
            fileUrl?: string;
            fileName?: string;
          };
          replyTo?: string;
          mentions?: string[];
        }) => {
          try {
            const { roomId, content, replyTo, mentions } = data;

            // Check if user is in the room
            if (user.currentRoom !== roomId) {
              socket.emit("error", {
                message: "You must be in the room to send messages",
              });
              return;
            }

            // Check content moderation
            if (content.text) {
              const moderationResult = await ModerationService.moderateContent(
                content.text
              );
              if (moderationResult.blocked) {
                socket.emit("message-blocked", {
                  reason: moderationResult.reason,
                  suggestions: moderationResult.suggestions,
                });
                return;
              }
            }

            // Send message
            const message = await ChatService.sendMessage(
              roomId,
              user.userId,
              user.anonymousId,
              user.displayName,
              { content, replyTo, mentions },
              {
                ipAddress: socket.handshake.address,
                userAgent: socket.handshake.headers["user-agent"],
              }
            );

            // Broadcast message to room
            const chatMessage: ChatMessage = {
              id: message._id.toString(),
              roomId,
              senderId: user.userId,
              anonymousSenderId: user.anonymousId,
              senderDisplayName: user.displayName,
              content: message.content,
              replyTo: message.replyTo?.toString(),
              mentions: message.mentions.map((m) => m.toString()),
              timestamp: message.createdAt,
            };

            this.io.to(roomId).emit("new-message", chatMessage);

            // Send mentions notifications
            if (mentions && mentions.length > 0) {
              this.sendMentionNotifications(
                roomId,
                mentions,
                user.displayName,
                content.text || ""
              );
            }

            logger.info(
              `Message sent in room ${roomId} by ${user.displayName}`
            );
          } catch (error) {
            logger.error("Send message error:", error);
            socket.emit("error", {
              message:
                error instanceof Error
                  ? error.message
                  : "Failed to send message",
            });
          }
        }
      );

      // Typing indicator events
      socket.on("typing-start", (data: { roomId: string }) => {
        if (user.currentRoom === data.roomId) {
          socket.to(data.roomId).emit("user-typing", {
            userId: user.userId,
            anonymousId: user.anonymousId,
            displayName: user.displayName,
          });
        }
      });

      socket.on("typing-stop", (data: { roomId: string }) => {
        if (user.currentRoom === data.roomId) {
          socket.to(data.roomId).emit("user-stopped-typing", {
            userId: user.userId,
            anonymousId: user.anonymousId,
            displayName: user.displayName,
          });
        }
      });

      // Edit message event
      socket.on(
        "edit-message",
        async (data: { messageId: string; newContent: string }) => {
          try {
            const { messageId, newContent } = data;

            // Check if user is authorized to edit this message
            const message = await ChatService.getMessage(messageId);
            if (!message) {
              socket.emit("error", { message: "Message not found" });
              return;
            }

            // Check if user owns the message
            const isOwner = user.userId
              ? message.senderId?.toString() === user.userId
              : message.anonymousSenderId === user.anonymousId;

            if (!isOwner) {
              socket.emit("error", {
                message: "You can only edit your own messages",
              });
              return;
            }

            // Edit the message
            const updatedMessage = await ChatService.editMessage(
              messageId,
              newContent
            );

            // Broadcast the edit to all users in the room
            this.io.to(message.chatRoomId.toString()).emit("message-edited", {
              messageId,
              newContent,
              editedAt: new Date(),
              isEdited: true,
            });

            logger.info(`Message ${messageId} edited by ${user.displayName}`);
          } catch (error) {
            logger.error("Edit message error:", error);
            socket.emit("error", { message: "Failed to edit message" });
          }
        }
      );

      // Delete message event
      socket.on("delete-message", async (data: { messageId: string }) => {
        try {
          const { messageId } = data;

          // Check if user is authorized to delete this message
          const message = await ChatService.getMessage(messageId);
          if (!message) {
            socket.emit("error", { message: "Message not found" });
            return;
          }

          // Check if user owns the message or is a moderator
          const isOwner = user.userId
            ? message.senderId?.toString() === user.userId
            : message.anonymousSenderId === user.anonymousId;

          // TODO: Add moderator check when user roles are implemented
          if (!isOwner) {
            socket.emit("error", {
              message: "You can only delete your own messages",
            });
            return;
          }

          // Delete the message using the proper method for user's own messages
          await ChatService.deleteOwnMessage(
            messageId,
            user.userId,
            user.anonymousId
          );

          // Broadcast the deletion to all users in the room
          this.io.to(message.chatRoomId.toString()).emit("message-deleted", {
            messageId,
            deletedAt: new Date(),
          });

          logger.info(`Message ${messageId} deleted by ${user.displayName}`);
        } catch (error) {
          logger.error("Delete message error:", error);
          socket.emit("error", { message: "Failed to delete message" });
        }
      });

      // Reaction events
      socket.on(
        "add-reaction",
        async (data: { messageId: string; emoji: string }) => {
          try {
            // TODO: Implement reaction system when Message model methods are available
            socket.emit("reaction-added", {
              messageId: data.messageId,
              emoji: data.emoji,
              userId: user.userId,
              anonymousId: user.anonymousId,
            });
          } catch (error) {
            logger.error("Add reaction error:", error);
          }
        }
      );

      // Disconnect event
      socket.on("disconnect", async () => {
        try {
          if (user.currentRoom) {
            await this.leaveRoom(socket, user.currentRoom);
          }

          this.connectedUsers.delete(socket.id);

          // Clean up Redis data
          try {
            const redisClient = getRedisClient();
            if (redisClient) {
              await redisClient.del(`user_room:${socket.id}`);
            }
          } catch (error) {
            logger.warn("Failed to clean up Redis data:", error);
          }

          logger.info(`Socket disconnected: ${user.displayName}`);
        } catch (error) {
          logger.error("Disconnect error:", error);
        }
      });
    });
  }

  /**
   * Handle leaving a room
   */
  private async leaveRoom(socket: any, roomId: string) {
    const user = socket.data.user as SocketUser;

    await ChatService.leaveChatRoom(roomId, user.userId, user.anonymousId);
    socket.leave(roomId);

    if (user.currentRoom === roomId) {
      user.currentRoom = undefined;
      this.connectedUsers.set(socket.id, user);
    }

    // Notify room about user leaving
    socket.to(roomId).emit("user-left", {
      userId: user.userId,
      anonymousId: user.anonymousId,
      displayName: user.displayName,
      timestamp: new Date(),
    });

    logger.info(`User ${user.displayName} left room ${roomId}`);
  }

  /**
   * Send mention notifications
   */
  private async sendMentionNotifications(
    roomId: string,
    mentions: string[],
    senderName: string,
    messageText: string
  ) {
    try {
      // Find mentioned users who are online in the room
      for (const [socketId, user] of this.connectedUsers) {
        if (
          user.currentRoom === roomId &&
          user.userId &&
          mentions.includes(user.userId)
        ) {
          this.io.to(socketId).emit("mention-notification", {
            roomId,
            senderName,
            messageText:
              messageText.substring(0, 100) +
              (messageText.length > 100 ? "..." : ""),
            timestamp: new Date(),
          });
        }
      }
    } catch (error) {
      logger.error("Send mention notifications error:", error);
    }
  }

  /**
   * Get online users in a room
   */
  public getOnlineUsersInRoom(roomId: string): SocketUser[] {
    const users: SocketUser[] = [];
    for (const user of this.connectedUsers.values()) {
      if (user.currentRoom === roomId) {
        users.push(user);
      }
    }
    return users;
  }

  /**
   * Send system message to room
   */
  public sendSystemMessage(roomId: string, message: string) {
    this.io.to(roomId).emit("system-message", {
      message,
      timestamp: new Date(),
    });
  }

  /**
   * Kick user from room (moderator action)
   */
  public async kickUserFromRoom(
    roomId: string,
    userId: string,
    reason?: string
  ) {
    try {
      for (const [socketId, user] of this.connectedUsers) {
        if (user.currentRoom === roomId && user.userId === userId) {
          const socket = this.io.sockets.sockets.get(socketId);
          if (socket) {
            socket.emit("kicked-from-room", { reason });
            await this.leaveRoom(socket, roomId);
          }
          break;
        }
      }
    } catch (error) {
      logger.error("Kick user error:", error);
    }
  }

  /**
   * Get Socket.IO instance
   */
  public getIO(): SocketIOServer {
    return this.io;
  }
}
