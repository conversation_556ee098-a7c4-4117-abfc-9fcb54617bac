import { Router } from "express";
import { AdminController } from "@/controllers/adminController";
import { paramValidations } from "@/utils/validation";
import {
  authenticate,
  requireAdmin,
  requireSuperAdmin,
} from "@/middleware/auth";
import { validateRequest } from "@/middleware/validateRequest";

const router = Router();

// Dashboard & Statistics
router.get(
  "/stats/dashboard",
  authenticate,
  requireAdmin,
  AdminController.getDashboardStats
);

router.get(
  "/metrics",
  authenticate,
  requireAdmin,
  AdminController.getPlatformMetrics
);

// Reports Management
router.get("/reports", authenticate, requireAdmin, AdminController.getReports);

router.put(
  "/reports/:reportId/status",
  authenticate,
  requireAdmin,
  paramValidations.objectId("reportId"),
  validateRequest,
  AdminController.updateReportStatus
);

// Admin Actions Log
router.get(
  "/actions",
  authenticate,
  requireAdmin,
  AdminController.getAdminActions
);

// System Settings
router.get(
  "/settings",
  authenticate,
  requireSuperAdmin,
  AdminController.getSystemSettings
);

router.put(
  "/settings",
  authenticate,
  requireSuperAdmin,
  AdminController.updateSystemSettings
);

// User Management
router.put(
  "/users/:userId/suspend",
  authenticate,
  requireAdmin,
  paramValidations.objectId("userId"),
  validateRequest,
  AdminController.suspendUser
);

// Counselor Management
router.put(
  "/counselors/:counselorId/suspend",
  authenticate,
  requireAdmin,
  paramValidations.objectId("counselorId"),
  validateRequest,
  AdminController.suspendCounselor
);

export default router;
